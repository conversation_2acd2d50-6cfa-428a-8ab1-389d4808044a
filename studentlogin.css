:root {
    --color-primary: #d17e7e;
    --color-primary-dark: #c06c6c;
    --color-secondary: #5a3e5d;
    --color-secondary-light: #8c6c8e;
    --color-secondary-dark: #4a3249;
    --color-bg-light: #f8f0eb;
    --color-bg-light-alt: #f5e6e0;
    --color-white: #ffffff;
    --color-text: #333333;
    --color-text-light: #666666;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, var(--color-bg-light) 0%, var(--color-bg-light-alt) 100%);
    min-height: 100vh;
    color: var(--color-text);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.login-card {
    max-width: 500px;
    margin: 0 auto;
    background: var(--color-white);
    border-radius: 16px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    padding: 40px 20px;
    text-align: center;
    position: relative;
}

.logo {
    width: 80px;
    height: 80px;
    background: var(--color-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 36px;
    font-weight: bold;
    color: var(--color-secondary);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.main-content {
    padding: 40px;
    flex: 1;
    background: var(--color-white);
    display: flex;
    flex-direction: column;
}

.title {
    font-size: 32px;
    font-weight: 700;
    color: var(--color-secondary);
    text-align: center;
    margin-bottom: 40px;
}

.login-form {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--color-text);
    font-size: 16px;
}

.form-group input {
    width: 100%;
    padding: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: var(--color-white);
}

.form-group input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

.form-group input::placeholder {
    color: #a0a0a0;
}

.signin-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
    color: var(--color-white);
    border: none;
    border-radius: 12px;
    padding: 18px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.signin-btn:hover {
    background: linear-gradient(135deg, var(--color-secondary-light) 0%, var(--color-secondary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(90, 62, 93, 0.3);
}

.social-divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
    color: var(--color-text-light);
    font-size: 14px;
}

.social-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.social-divider span {
    background: var(--color-white);
    padding: 0 20px;
}

.google-btn {
    width: 100%;
    background: var(--color-secondary);
    color: var(--color-white);
    border: none;
    border-radius: 12px;
    padding: 18px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 40px;
}

.google-btn:hover {
    background: #333;
    transform: translateY(-1px);
}

.google-icon {
    flex-shrink: 0;
}

.footer-links {
    margin-top: auto;
    padding-top: 20px;
}

.footer-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.footer-row:last-child {
    justify-content: flex-start;
    margin-bottom: 0;
}

.footer-links a {
    color: var(--color-text-light);
    text-decoration: none;
    font-size: 16px;
    transition: color 0.3s ease;
}

.forgot-link {
    color: var(--color-primary) !important;
}

.signup-link {
    color: var(--color-primary) !important;
    font-weight: 600;
}

.privacy-link {
    color: var(--color-text-light) !important;
}

.footer-links a:hover {
    color: var(--color-primary);
}

.error-message {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 8px;
    display: none;
}

.form-group.error input {
    border-color: #e74c3c;
}

.form-group.error .error-message {
    display: block;
}

/* Responsive design */
@media (max-width: 500px) {
    .container {
        max-width: 100%;
        box-shadow: none;
    }

    .main-content {
        padding: 30px 20px;
    }

    .title {
        font-size: 28px;
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group input {
        padding: 14px;
    }

    .footer-row {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .footer-row:last-child {
        justify-content: center;
    }
}