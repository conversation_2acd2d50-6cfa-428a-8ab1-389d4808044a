// Employer Profile Management with Firebase Integration
import AuthService from './auth-service.js';
import DatabaseService from './database-service.js';

class EmployerProfileManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    async init() {
        // Listen for auth state changes
        AuthService.onAuthStateChange((user) => {
            this.currentUser = user;
            if (user) {
                this.loadUserProfile();
            } else {
                // Redirect to login if not authenticated
                window.location.href = 'employer-signin.html';
            }
        });

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Profile update form
        const profileForm = document.getElementById('profile-form');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => this.handleProfileUpdate(e));
        }

        // Email update form
        const emailForm = document.getElementById('email-form');
        if (emailForm) {
            emailForm.addEventListener('submit', (e) => this.handleEmailUpdate(e));
        }

        // Password update form
        const passwordForm = document.getElementById('password-form');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => this.handlePasswordUpdate(e));
        }

        // Modal triggers
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal]')) {
                this.openModal(e.target.dataset.modal);
            }
        });
    }

    async loadUserProfile() {
        try {
            const userData = await AuthService.getCurrentUserData();
            if (userData) {
                this.populateProfileData(userData);
            }
        } catch (error) {
            console.error('Error loading profile:', error);
            this.showError('Failed to load profile data');
        }
    }

    populateProfileData(userData) {
        // Update profile display
        const elements = {
            'profile-name': userData.name || 'Not set',
            'profile-email': userData.email || 'Not set',
            'profile-phone': userData.phone || 'Not set',
            'profile-organization': userData.organizationType || 'Not set',
            'profile-company': userData.companyName || 'Not set',
            'profile-license': userData.licenseNumber || 'Not set',
            'profile-employees': userData.employeeCount || 'Not set'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // Update form fields
        const formElements = {
            'employer-name': userData.name,
            'employer-email': userData.email,
            'employer-phone': userData.phone,
            'organization-type': userData.organizationType,
            'company-name': userData.companyName,
            'license-number': userData.licenseNumber,
            'employee-count': userData.employeeCount
        };

        Object.entries(formElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element && value) {
                element.value = value;
            }
        });
    }

    async handleProfileUpdate(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const profileData = {
            name: formData.get('name'),
            phone: formData.get('phone'),
            organizationType: formData.get('organizationType'),
            companyName: formData.get('companyName'),
            licenseNumber: formData.get('licenseNumber'),
            employeeCount: formData.get('employeeCount')
        };

        this.showLoading('profile-update-btn', true);

        try {
            const result = await AuthService.updateUserProfile(profileData);
            
            if (result.success) {
                this.showSuccess(result.message);
                this.loadUserProfile(); // Refresh profile data
                this.closeModal();
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Failed to update profile');
        } finally {
            this.showLoading('profile-update-btn', false);
        }
    }

    async handleEmailUpdate(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const newEmail = formData.get('newEmail');
        const currentPassword = formData.get('currentPassword');

        if (!this.validateEmail(newEmail)) {
            this.showError('Please enter a valid email address');
            return;
        }

        this.showLoading('email-update-btn', true);

        try {
            const result = await AuthService.updateUserEmail(newEmail, currentPassword);
            
            if (result.success) {
                this.showSuccess(result.message);
                this.loadUserProfile(); // Refresh profile data
                this.closeModal();
                e.target.reset(); // Clear form
            } else {
                this.showError(result.error);
                if (result.requiresReauth) {
                    // Handle re-authentication requirement
                    this.handleReauthRequired();
                }
            }
        } catch (error) {
            this.showError('Failed to update email');
        } finally {
            this.showLoading('email-update-btn', false);
        }
    }

    async handlePasswordUpdate(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');

        // Validate passwords match
        if (newPassword !== confirmPassword) {
            this.showError('New passwords do not match');
            return;
        }

        this.showLoading('password-update-btn', true);

        try {
            const result = await AuthService.updateUserPassword(currentPassword, newPassword);
            
            if (result.success) {
                this.showSuccess(result.message);
                this.closeModal();
                e.target.reset(); // Clear form
            } else {
                this.showError(result.error);
                if (result.requiresReauth) {
                    this.handleReauthRequired();
                }
            }
        } catch (error) {
            this.showError('Failed to update password');
        } finally {
            this.showLoading('password-update-btn', false);
        }
    }

    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    handleReauthRequired() {
        this.showError('For security reasons, please sign out and sign in again to make this change');
        setTimeout(() => {
            AuthService.signOut().then(() => {
                window.location.href = 'employer-signin.html';
            });
        }, 3000);
    }

    openModal(modalType) {
        const modalContent = this.getModalContent(modalType);
        if (modalContent) {
            document.getElementById('modal-title').textContent = modalContent.title;
            document.getElementById('modal-body').innerHTML = modalContent.body;
            document.getElementById('modal-overlay').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        document.getElementById('modal-overlay').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    getModalContent(type) {
        const contents = {
            'edit-profile': {
                title: 'Edit Profile',
                body: `
                    <form id="profile-form">
                        <div class="form-group">
                            <label for="employer-name">Full Name</label>
                            <input type="text" id="employer-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="employer-phone">Phone Number</label>
                            <input type="tel" id="employer-phone" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label for="organization-type">Organization Type</label>
                            <select id="organization-type" name="organizationType" required>
                                <option value="">Select type</option>
                                <option value="government">Government</option>
                                <option value="NGO">NGO</option>
                                <option value="start-up">Start-up</option>
                                <option value="Enterprise">Enterprise</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="company-name">Company Name</label>
                            <input type="text" id="company-name" name="companyName">
                        </div>
                        <div class="form-group">
                            <label for="license-number">License Number</label>
                            <input type="text" id="license-number" name="licenseNumber">
                        </div>
                        <div class="form-group">
                            <label for="employee-count">Employee Count</label>
                            <select id="employee-count" name="employeeCount">
                                <option value="">Select range</option>
                                <option value="1-10">1-10</option>
                                <option value="11-50">11-50</option>
                                <option value="51-200">51-200</option>
                                <option value="200+">200+</option>
                            </select>
                        </div>
                        <button type="submit" id="profile-update-btn" class="btn-primary">Update Profile</button>
                    </form>
                `
            },
            'change-email': {
                title: 'Change Email',
                body: `
                    <form id="email-form">
                        <div class="form-group">
                            <label for="new-email">New Email</label>
                            <input type="email" id="new-email" name="newEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="current-password-email">Current Password</label>
                            <input type="password" id="current-password-email" name="currentPassword" required>
                            <small>Required for security verification</small>
                        </div>
                        <button type="submit" id="email-update-btn" class="btn-primary">Update Email</button>
                    </form>
                `
            },
            'change-password': {
                title: 'Change Password',
                body: `
                    <form id="password-form">
                        <div class="form-group">
                            <label for="current-password">Current Password</label>
                            <input type="password" id="current-password" name="currentPassword" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">New Password</label>
                            <input type="password" id="new-password" name="newPassword" required>
                            <small>Must be at least 6 characters with uppercase, lowercase, and number</small>
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">Confirm New Password</label>
                            <input type="password" id="confirm-password" name="confirmPassword" required>
                        </div>
                        <button type="submit" id="password-update-btn" class="btn-primary">Update Password</button>
                    </form>
                `
            }
        };

        return contents[type];
    }

    showLoading(buttonId, show) {
        const button = document.getElementById(buttonId);
        if (button) {
            if (show) {
                button.disabled = true;
                button.textContent = 'Updating...';
            } else {
                button.disabled = false;
                button.textContent = button.textContent.replace('Updating...', 'Update');
            }
        }
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessage = document.querySelector('.message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            padding: 12px;
            margin: 10px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            ${type === 'error' ? 
                'background: #fef2f2; color: #dc2626; border: 1px solid #fecaca;' : 
                'background: #f0fdf4; color: #059669; border: 1px solid #bbf7d0;'
            }
        `;

        document.body.appendChild(messageDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EmployerProfileManager();
});
