:root {
    --color-primary: #d17e7e;
    --color-primary-dark: #c06c6c;
    --color-secondary: #5a3e5d;
    --color-secondary-light: #8c6c8e;
    --color-secondary-dark: #4a3249;
    --color-bg-light: #f8f0eb;
    --color-bg-light-alt: #f5e6e0;
    --color-white: #ffffff;
    --color-text: #333333;
    --color-text-light: #666666;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, var(--color-bg-light) 0%, var(--color-bg-light-alt) 100%);
    min-height: 100vh;
    color: var(--color-text);
}

.container {
    max-width: 600px;
    margin: 0 auto;
    min-height: 100vh;
    background: var(--color-white);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    background: var(--color-white);
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--color-text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: var(--color-bg-light);
    color: var(--color-text);
}

.main-content {
    padding: 0 30px 40px;
}

.profile-section {
    text-align: center;
    margin-bottom: 40px;
}

.profile-picture {
    width: 120px;
    height: 120px;
    background: var(--color-bg-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.profile-picture:hover {
    background: var(--color-bg-light-alt);
    transform: scale(1.05);
}

.profile-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-icon {
    font-size: 48px;
    color: var(--color-text-light);
}

.profile-name {
    font-size: 28px;
    font-weight: 700;
    color: var(--color-secondary);
    margin-bottom: 8px;
}

.profile-menu {
    background: var(--color-white);
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #e1e5e9;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-item:hover {
    background: var(--color-bg-light);
    padding-left: 10px;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 16px;
    background: var(--color-bg-light);
    border-radius: 8px;
}

.menu-text {
    flex: 1;
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
}

.menu-arrow {
    font-size: 20px;
    color: var(--color-text-light);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: var(--color-white);
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e1e5e9;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: var(--color-white);
    border-radius: 16px 16px 0 0;
}

.modal-header h2 {
    font-size: 24px;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--color-white);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--color-text);
    font-size: 16px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: var(--color-white);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: var(--color-text);
}

.info-value {
    color: var(--color-text-light);
    text-align: right;
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
    color: var(--color-white);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--color-secondary-light) 0%, var(--color-secondary) 100%);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--color-bg-light);
    color: var(--color-text);
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 12px;
}

.btn-secondary:hover {
    background: var(--color-bg-light-alt);
    border-color: var(--color-primary);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
        box-shadow: none;
    }
    
    .main-content {
        padding: 0 20px 40px;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .menu-item {
        padding: 16px 0;
    }
    
    .menu-text {
        font-size: 16px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 700px;
    }
}