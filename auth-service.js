// Authentication Service
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
  onAuthStateChanged,
  updateProfile,
  reauthenticateWithCredential,
  EmailAuthProvider,
  updateEmail,
  updatePassword
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db, googleProvider } from './firebase-config.js';

class AuthService {
  // Sign up with email and password
  async signUp(email, password, userData) {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Update user profile
      await updateProfile(user, {
        displayName: userData.name
      });
      
      // Save user data to Firestore
      await this.saveUserData(user.uid, {
        ...userData,
        email: user.email,
        createdAt: new Date(),
        lastLogin: new Date()
      });
      
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // Sign in with email and password
  async signIn(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      
      // Update last login
      await this.updateLastLogin(userCredential.user.uid);
      
      return { success: true, user: userCredential.user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // Sign in with Google
  async signInWithGoogle() {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;
      
      // Check if user exists in Firestore
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      
      if (!userDoc.exists()) {
        // New user - save basic data
        await this.saveUserData(user.uid, {
          name: user.displayName,
          email: user.email,
          photoURL: user.photoURL,
          provider: 'google',
          createdAt: new Date(),
          lastLogin: new Date()
        });
      } else {
        // Existing user - update last login
        await this.updateLastLogin(user.uid);
      }
      
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // Sign out
  async signOut() {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // Save user data to Firestore
  async saveUserData(uid, userData) {
    try {
      await setDoc(doc(db, 'users', uid), userData);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // Update last login
  async updateLastLogin(uid) {
    try {
      await setDoc(doc(db, 'users', uid), {
        lastLogin: new Date()
      }, { merge: true });
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  }
  
  // Get current user data
  async getCurrentUserData() {
    const user = auth.currentUser;
    if (!user) return null;
    
    try {
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (userDoc.exists()) {
        return { uid: user.uid, ...userDoc.data() };
      }
      return null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }
  
  // Update user email
  async updateUserEmail(newEmail, currentPassword) {
    const user = auth.currentUser;

    if (!user) {
      return { success: false, error: 'Please sign in to update your email', requiresAuth: true };
    }

    try {
      // Re-authenticate first
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Now update email
      await updateEmail(user, newEmail);

      // Update email in Firestore
      await updateDoc(doc(db, 'users', user.uid), {
        email: newEmail,
        updatedAt: serverTimestamp()
      });

      return { success: true, message: 'Email updated successfully' };
    } catch (error) {
      return this.handleAuthError(error);
    }
  }

  // Update user password
  async updateUserPassword(currentPassword, newPassword) {
    const user = auth.currentUser;

    if (!user) {
      return { success: false, error: 'Please sign in to update your password', requiresAuth: true };
    }

    // Validate new password
    const validation = this.validatePassword(newPassword);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    try {
      // Re-authenticate first
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Now update password
      await updatePassword(user, newPassword);

      // Update timestamp in Firestore
      await updateDoc(doc(db, 'users', user.uid), {
        passwordUpdatedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return { success: true, message: 'Password updated successfully' };
    } catch (error) {
      return this.handleAuthError(error);
    }
  }

  // Update user profile information
  async updateUserProfile(profileData) {
    const user = auth.currentUser;

    if (!user) {
      return { success: false, error: 'Please sign in to update your profile', requiresAuth: true };
    }

    try {
      // Update display name in Firebase Auth if provided
      if (profileData.name) {
        await updateProfile(user, {
          displayName: profileData.name
        });
      }

      // Update profile data in Firestore
      await updateDoc(doc(db, 'users', user.uid), {
        ...profileData,
        updatedAt: serverTimestamp()
      });

      return { success: true, message: 'Profile updated successfully' };
    } catch (error) {
      return { success: false, error: 'Failed to update profile. Please try again.' };
    }
  }

  // Validate password strength
  validatePassword(password) {
    if (!password || password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters long' };
    }

    if (!/(?=.*[a-z])/.test(password)) {
      return { valid: false, error: 'Password must contain at least one lowercase letter' };
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      return { valid: false, error: 'Password must contain at least one uppercase letter' };
    }

    if (!/(?=.*\d)/.test(password)) {
      return { valid: false, error: 'Password must contain at least one number' };
    }

    return { valid: true };
  }

  // Handle authentication errors
  handleAuthError(error) {
    console.error('Auth error:', error);

    switch (error.code) {
      case 'auth/email-already-in-use':
        return { success: false, error: 'This email is already registered with another account' };

      case 'auth/invalid-email':
        return { success: false, error: 'Please enter a valid email address' };

      case 'auth/requires-recent-login':
        return {
          success: false,
          error: 'For security reasons, please sign in again to make this change',
          requiresReauth: true
        };

      case 'auth/wrong-password':
        return { success: false, error: 'Current password is incorrect' };

      case 'auth/weak-password':
        return { success: false, error: 'Password is too weak. Please choose a stronger password' };

      case 'auth/network-request-failed':
        return { success: false, error: 'Network error. Please check your connection and try again' };

      case 'auth/too-many-requests':
        return { success: false, error: 'Too many failed attempts. Please try again later' };

      default:
        return {
          success: false,
          error: error.message || 'An unexpected error occurred. Please try again.'
        };
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback) {
    return onAuthStateChanged(auth, callback);
  }
}

export default new AuthService();
