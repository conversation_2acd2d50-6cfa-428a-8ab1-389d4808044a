/* Custom color variables */
:root {
  --color-primary: #d17e7e;
  --color-primary-dark: #c06c6c;
  --color-secondary: #5a3e5d;
  --color-secondary-light: #8c6c8e;
  --color-secondary-dark: #4a3249;
  --color-bg-light: #f8f0eb;
  --color-bg-light-alt: #f5e6e0;
  --color-white: #ffffff;
  --color-text: #333333;
  --color-text-light: #666666;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--color-bg-light);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
}

.signup-card {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  padding: 40px 20px;
  text-align: center;
}

.logo-container {
  display: flex;
  justify-content: center;
}

.logo {
  width: 60px;
  height: 60px;
  background: var(--color-white);
  color: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: bold;
  box-shadow: 0 6px 20px rgba(209, 126, 126, 0.4);
}

.content {
  padding: 40px 30px;
  background: var(--color-white);
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text);
  text-align: center;
  margin-bottom: 35px;
  letter-spacing: -0.5px;
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-bottom: 25px;
}

.form-field {
  position: relative;
}

.form-field input,
.form-field select {
  width: 100%;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 16px;
  background: var(--color-white);
  color: var(--color-text);
  transition: all 0.3s ease;
  appearance: none;
}

.form-field input:focus,
.form-field select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

.form-field input::placeholder {
  color: var(--color-text-light);
  font-size: 16px;
}

.form-field select {
  background-image: none;
  cursor: pointer;
  color: var(--color-text-light);
}

.form-field select:focus,
.form-field select:valid {
  color: var(--color-text);
}

.dropdown-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-light);
  pointer-events: none;
  font-size: 12px;
}

.btn-signup {
  width: 100%;
background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
  color: var(--color-white);
  border: none;
  padding: 18px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 15px;
  box-shadow: 0 6px 20px rgba(209, 126, 126, 0.3);
}

.btn-signup:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(209, 126, 126, 0.4);
}

.btn-signup:active {
  transform: translateY(0);
}

.btn-google {
  width: 100%;
  background: var(--color-secondary);
  color: var(--color-white);
  border: none;
  padding: 18px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.btn-google:hover {
  background: var(--color-secondary-light);
  transform: translateY(-1px);
}

.btn-google:active {
  background: var(--color-secondary-dark);
  transform: translateY(0);
}

.google-icon {
  flex-shrink: 0;
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.divider span {
  background: var(--color-white);
  padding: 0 20px;
  color: var(--color-text-light);
  font-size: 14px;
}

.signin-link {
  text-align: center;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.signin-link p {
  color: var(--color-text-light);
  font-size: 14px;
}

.signin-link a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signin-link a:hover {
  color: var(--color-primary-dark);
}

/* Form validation styles */
.form-field input.error,
.form-field select.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-field input.success,
.form-field select.success {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

/* Responsive adjustments */
@media (max-width: 500px) {
  .container {
    max-width: 100%;
  }

  .signup-card {
    border-radius: 12px;
  }

  .content {
    padding: 30px 20px;
  }

  .form-field input,
  .form-field select {
    padding: 12px;
    font-size: 16px;
  }
}

/* Animation for page load */
.signup-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom select styling for different browsers */
.form-field select::-ms-expand {
  display: none;
}

.form-field select option {
  background: var(--color-white);
  color: var(--color-text);
  padding: 10px;
}

/* Focus states for better accessibility */
.form-field input:focus,
.form-field select:focus,
.btn-signup:focus,
.btn-google:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
