/* Custom color variables */
:root {
  --color-primary: #d17e7e;
  --color-primary-dark: #c06c6c;
  --color-secondary: #5a3e5d;
  --color-secondary-light: #8c6c8e;
  --color-secondary-dark: #4a3249;
  --color-bg-light: #f8f0eb;
  --color-bg-light-alt: #f5e6e0;
  --color-white: #ffffff;
  --color-text: #333333;
  --color-text-light: #666666;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--color-bg-light);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background: var(--color-white);
  min-height: 100vh;
  position: relative;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--color-white);
}

.back-btn {
  background: none;
  border: none;
  color: var(--color-white);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.logo {
  width: 40px;
  height: 40px;
  background: var(--color-white);
  color: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.spacer {
  width: 40px;
}

.content {
  flex: 1;
  padding: 30px 20px;
  overflow-y: auto;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text);
  text-align: center;
  margin-bottom: 30px;
  letter-spacing: -0.5px;
}

.job-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.half {
  flex: 1;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-group label {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 8px;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 15px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  background: var(--color-white);
  color: var(--color-text);
  transition: all 0.3s ease;
  font-family: inherit;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--color-text-light);
  font-size: 14px;
}

.form-group textarea {
  min-height: 120px;
  line-height: 1.5;
}

.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d17e7e' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  cursor: pointer;
}

.payment-section {
  background: var(--color-bg-light-alt);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid rgba(209, 126, 126, 0.2);
}

.publish-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 20px;
  box-shadow: 0 4px 15px rgba(209, 126, 126, 0.3);
}

.publish-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(209, 126, 126, 0.4);
}

.publish-btn:active {
  transform: translateY(0);
}

.bottom-nav {
  display: flex;
  background: var(--color-white);
  border-top: 1px solid #e5e7eb;
  padding: 12px 0;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--color-text-light);
  font-size: 12px;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 8px;
}

.nav-item:hover,
.nav-item.active {
  color: var(--color-primary);
}

.nav-item svg {
  margin-bottom: 4px;
}

.nav-item span {
  font-size: 11px;
}

/* Form validation styles */
.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group input.success,
.form-group textarea.success,
.form-group select.success {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }

  .content {
    padding: 20px 15px;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 12px;
    font-size: 16px;
  }
}

/* Animation for page load */
.container {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for better accessibility */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus,
.publish-btn:focus,
.back-btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
