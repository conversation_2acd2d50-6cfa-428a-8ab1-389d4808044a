// Employer Signup with Firebase Integration
import AuthService from './auth-service.js';
import DatabaseService from './database-service.js';

class EmployerSignup {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Form submission
        document.getElementById('employer-signup-form').addEventListener('submit', (e) => {
            this.handleSignup(e);
        });

        // Google signup
        document.querySelector('.btn-google').addEventListener('click', () => {
            this.handleGoogleSignup();
        });
    }

    async handleSignup(e) {
        e.preventDefault();
        
        const formData = this.getFormData();
        
        if (!this.validateForm(formData)) {
            return;
        }

        this.showLoading(true);

        try {
            // Create user account
            const result = await AuthService.signUp(formData.email, formData.password, {
                name: formData.name,
                organizationType: formData.organizationType,
                phone: formData.phone,
                userType: 'employer'
            });

            if (result.success) {
                this.showSuccess('Account created successfully!');
                // Redirect to signin page
                setTimeout(() => {
                    window.location.href = 'employer-signin.html';
                }, 2000);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('An unexpected error occurred. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }

    async handleGoogleSignup() {
        this.showLoading(true);

        try {
            const result = await AuthService.signInWithGoogle();
            
            if (result.success) {
                // Update user type to employer
                await DatabaseService.updateUserProfile(result.user.uid, {
                    userType: 'employer'
                });
                
                this.showSuccess('Account created successfully with Google!');
                setTimeout(() => {
                    window.location.href = 'employer-signin.html';
                }, 2000);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('Google signup failed. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }

    getFormData() {
        return {
            organizationType: document.getElementById('organization-type').value,
            name: document.getElementById('employer-name').value,
            email: document.getElementById('employer-email').value,
            phone: document.getElementById('employer-phone').value,
            password: document.getElementById('employer-password').value
        };
    }

    validateForm(data) {
        if (!data.organizationType || !data.name || !data.email || !data.phone || !data.password) {
            this.showError('Please fill in all required fields');
            return false;
        }

        if (!this.isValidEmail(data.email)) {
            this.showError('Please enter a valid email address');
            return false;
        }

        if (data.password.length < 6) {
            this.showError('Password must be at least 6 characters long');
            return false;
        }

        return true;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showLoading(show) {
        const submitBtn = document.querySelector('.btn-signup');
        if (show) {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating Account...';
        } else {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Sign Up';
        }
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessage = document.querySelector('.message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            padding: 12px;
            margin: 10px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            ${type === 'error' ? 
                'background: #fef2f2; color: #dc2626; border: 1px solid #fecaca;' : 
                'background: #f0fdf4; color: #059669; border: 1px solid #bbf7d0;'
            }
        `;

        // Insert message
        const form = document.getElementById('employer-signup-form');
        form.insertBefore(messageDiv, form.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EmployerSignup();
});
