/* Custom color variables */
:root {
  --color-primary: #d17e7e;
  --color-primary-dark: #c06c6c;
  --color-secondary: #5a3e5d;
  --color-secondary-light: #8c6c8e;
  --color-secondary-dark: #4a3249;
  --color-bg-light: #f8f0eb;
  --color-bg-light-alt: #f5e6e0;
  --color-white: #ffffff;
  --color-text: #333333;
  --color-text-light: #666666;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--color-bg-light);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.phone-container {
  width: 100%;
  max-width: 375px;
  margin: 0 auto;
}

.phone-frame {
  background: var(--color-white);
  border-radius: 25px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 8px solid #2c2c2c;
  position: relative;
}

.phone-header {
  background: #2c2c2c;
  height: 30px;
  position: relative;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 100%;
  color: var(--color-white);
  font-size: 14px;
  font-weight: 600;
}

.time {
  font-size: 14px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-bars {
  display: flex;
  gap: 2px;
}

.signal-bars span {
  width: 3px;
  height: 10px;
  background: var(--color-white);
  border-radius: 1px;
}

.signal-bars span:nth-child(1) {
  height: 4px;
}
.signal-bars span:nth-child(2) {
  height: 6px;
}
.signal-bars span:nth-child(3) {
  height: 8px;
}
.signal-bars span:nth-child(4) {
  height: 10px;
}

.app-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  padding: 30px 20px;
  text-align: center;
}

.logo-container {
  display: flex;
  justify-content: center;
}

.logo {
  width: 50px;
  height: 50px;
  background: var(--color-white);
  color: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(209, 126, 126, 0.3);
}

.app-content {
  padding: 30px 20px;
  background: var(--color-white);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-text);
  text-align: center;
  margin-bottom: 30px;
  letter-spacing: -0.5px;
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.form-field {
  position: relative;
}

.form-field input,
.form-field select {
  width: 100%;
  padding: 15px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  background: var(--color-white);
  color: var(--color-text);
  transition: all 0.3s ease;
  appearance: none;
}

.form-field input:focus,
.form-field select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

.form-field input::placeholder {
  color: var(--color-text-light);
  font-size: 16px;
}

.form-field select {
  background-image: none;
  cursor: pointer;
  color: var(--color-text-light);
}

.form-field select:focus,
.form-field select:valid {
  color: var(--color-text);
}

.dropdown-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-light);
  pointer-events: none;
  font-size: 12px;
}

.btn-signup {
  width: 100%;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: var(--color-white);
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 10px;
  box-shadow: 0 4px 15px rgba(209, 126, 126, 0.3);
}

.btn-signup:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(209, 126, 126, 0.4);
}

.btn-signup:active {
  transform: translateY(0);
}

.btn-google {
  width: 100%;
  background: var(--color-secondary);
  color: var(--color-white);
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.btn-google:hover {
  background: var(--color-secondary-light);
  transform: translateY(-1px);
}

.btn-google:active {
  background: var(--color-secondary-dark);
  transform: translateY(0);
}

.google-icon {
  flex-shrink: 0;
}

/* Form validation styles */
.form-field input.error,
.form-field select.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-field input.success,
.form-field select.success {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .phone-container {
    max-width: 100%;
  }

  .phone-frame {
    border-width: 4px;
    border-radius: 20px;
  }

  .app-content {
    padding: 25px 15px;
  }

  .form-field input,
  .form-field select {
    padding: 12px;
    font-size: 16px;
  }
}

/* Animation for page load */
.phone-frame {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom select styling for different browsers */
.form-field select::-ms-expand {
  display: none;
}

.form-field select option {
  background: var(--color-white);
  color: var(--color-text);
  padding: 10px;
}

/* Focus states for better accessibility */
.form-field input:focus,
.form-field select:focus,
.btn-signup:focus,
.btn-google:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
