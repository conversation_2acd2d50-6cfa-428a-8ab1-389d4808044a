// Database Service
import { 
  collection, 
  doc, 
  addDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './firebase-config.js';

class DatabaseService {
  // Job Management
  async createJob(jobData) {
    try {
      const docRef = await addDoc(collection(db, 'jobs'), {
        ...jobData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: 'active'
      });
      return { success: true, jobId: docRef.id };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async getJobs(filters = {}) {
    try {
      let q = collection(db, 'jobs');
      
      // Apply filters
      if (filters.category) {
        q = query(q, where('category', '==', filters.category));
      }
      if (filters.employerId) {
        q = query(q, where('employerId', '==', filters.employerId));
      }
      if (filters.status) {
        q = query(q, where('status', '==', filters.status));
      }
      
      // Order by creation date
      q = query(q, orderBy('createdAt', 'desc'));
      
      // Apply limit if specified
      if (filters.limit) {
        q = query(q, limit(filters.limit));
      }
      
      const querySnapshot = await getDocs(q);
      const jobs = [];
      querySnapshot.forEach((doc) => {
        jobs.push({ id: doc.id, ...doc.data() });
      });
      
      return { success: true, jobs };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async getJob(jobId) {
    try {
      const docRef = doc(db, 'jobs', jobId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { success: true, job: { id: docSnap.id, ...docSnap.data() } };
      } else {
        return { success: false, error: 'Job not found' };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async updateJob(jobId, updates) {
    try {
      const docRef = doc(db, 'jobs', jobId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: serverTimestamp()
      });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async deleteJob(jobId) {
    try {
      await deleteDoc(doc(db, 'jobs', jobId));
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // User Profile Management
  async updateUserProfile(userId, profileData) {
    try {
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, {
        ...profileData,
        updatedAt: serverTimestamp()
      });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async getUserProfile(userId) {
    try {
      const docRef = doc(db, 'users', userId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { success: true, profile: docSnap.data() };
      } else {
        return { success: false, error: 'User not found' };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  // Job Applications
  async applyForJob(jobId, studentId, applicationData) {
    try {
      const docRef = await addDoc(collection(db, 'applications'), {
        jobId,
        studentId,
        ...applicationData,
        status: 'pending',
        appliedAt: serverTimestamp()
      });
      return { success: true, applicationId: docRef.id };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async getApplications(filters = {}) {
    try {
      let q = collection(db, 'applications');
      
      if (filters.jobId) {
        q = query(q, where('jobId', '==', filters.jobId));
      }
      if (filters.studentId) {
        q = query(q, where('studentId', '==', filters.studentId));
      }
      if (filters.status) {
        q = query(q, where('status', '==', filters.status));
      }
      
      q = query(q, orderBy('appliedAt', 'desc'));
      
      const querySnapshot = await getDocs(q);
      const applications = [];
      querySnapshot.forEach((doc) => {
        applications.push({ id: doc.id, ...doc.data() });
      });
      
      return { success: true, applications };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
  
  async updateApplicationStatus(applicationId, status) {
    try {
      const docRef = doc(db, 'applications', applicationId);
      await updateDoc(docRef, {
        status,
        updatedAt: serverTimestamp()
      });
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

export default new DatabaseService();
