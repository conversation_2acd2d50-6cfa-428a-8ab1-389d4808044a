<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="employer-styles.css">
</head>
<body>
    <div class="container">
        <div class="signup-card">
            <div class="header">
                <div class="logo-container">
                    <div class="logo">S</div>
                </div>
            </div>

            <div class="content">
                <h1 class="page-title">Employer Sign Up</h1>
                
                <form id="employer-signup-form" class="signup-form">
                    <div class="form-field">
                        <select id="employer-type" required>
                            <option value="">Select your organization type</option>
                            <option value="law-firm">government</option>
                            <option value="corporate">NGO</option>
                            <option value="government">start-up</option>
                            <option value="non-profit">Enterprise</option>
                            
                        </select>
                        <div class="dropdown-arrow">▼</div>
                    </div>
                    
                    <div class="form-field">
                        <input type="text" id="employer-name" placeholder="Full Name / Company Name" required>
                    </div>
                    
                    <div class="form-field">
                        <input type="text" id="employer-reg" placeholder="Company Registration Number (Optional)">
                    </div>
                    
                    <div class="form-field">
                        <input type="email" id="employer-email" placeholder="Email" required>
                    </div>
                    
                    <div class="form-field">
                        <input type="tel" id="employer-phone" placeholder="Phone Number" required>
                    </div>
                    
                    <div class="form-field">
                        <input type="password" id="employer-password" placeholder="Password" required>
                    </div>
                    
                    <button type="submit" class="btn-signup">Sign Up</button>
                </form>
                
                <button class="btn-google" onclick="handleGoogleSignUp()">
                    <svg class="google-icon" viewBox="0 0 24 24" width="18" height="18">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Sign Up with Google
                </button>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('employer-signup-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const employerType = document.getElementById('employer-type').value;
            const name = document.getElementById('employer-name').value;
            const email = document.getElementById('employer-email').value;
            const phone = document.getElementById('employer-phone').value;
            const password = document.getElementById('employer-password').value;
            
            if (employerType && name && email && phone && password) {
                alert(`Welcome ${name}! Your employer account has been created successfully.`);
                window.location.href = 'employer-signin.html';
            } else {
                alert('Please fill in all required fields');
            }
        });

        function handleGoogleSignUp() {
            // Redirect to Google account chooser with source parameter
            window.location.href = 'google-account-chooser.html?source=signup';
        }

        // Phone number formatting
        document.getElementById('employer-phone').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
            }
            this.value = value;
        });

        // Form validation
        const inputs = document.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value) {
                    this.style.borderColor = '#dc2626';
                } else if (this.type === 'email' && this.value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(this.value)) {
                        this.style.borderColor = '#dc2626';
                    } else {
                        this.style.borderColor = 'var(--color-primary)';
                    }
                } else {
                    this.style.borderColor = '#d1d5db';
                }
            });
        });
    </script>
</body>
</html>
