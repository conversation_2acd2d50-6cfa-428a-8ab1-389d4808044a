<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legal Career Platform - Employer Profile</title>
    <link rel="stylesheet" href="employer-profile-styles.css">
</head>
<body>
    <div class="container">
        <div class="profile-card">
            <div class="header">
                <div class="close-btn" onclick="goBack()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

        <div class="profile-section">
            <div class="profile-avatar">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
            </div>
            <h1 class="company-name">Law Firm</h1>
        </div>

        <div class="menu-section">
            <div class="menu-item" onclick="showCompanyInfo()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M3 21H21M5 21V7L13 2L21 7V21M9 9H11M9 12H11M9 15H11M13 9H15M13 12H15M13 15H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Company Information</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="menu-item" onclick="showLicenseInfo()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">License</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="menu-item" onclick="showEmployeeInfo()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 1.17157 16.1716C0.421427 16.9217 0 17.9391 0 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        <path d="M23 21V19C23 18.1332 22.7361 17.2986 22.2416 16.5956C21.7471 15.8927 21.0424 15.3581 20.2209 15.0657" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 18.9078 6.11683 18.7563 6.96353C18.6047 7.81023 18.1147 8.58044 17.3789 9.10338C16.6431 9.62632 15.7556 9.86614 14.8889 9.7859" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Employee Information</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

            <div class="menu-item" onclick="showJobInfo()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M20 7H4C2.89543 7 2 7.89543 2 9V19C2 20.1046 2.89543 21 4 21H20C21.1046 21 22 20.1046 22 19V9C22 7.89543 21.1046 7 20 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 21V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Job Postings</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="settings-section">
            <div class="menu-item" onclick="showSettings()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.01127 9.77251C4.28054 9.5799 4.48571 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Settings</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for different sections -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Section Title</h2>
                <button class="modal-close" onclick="closeModal()">✕</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Content will be dynamically loaded here -->
            </div>
        </div>
        </div>
    </div>

    <script>
        // Sample data (in a real app, this would come from the database)
        const employerData = {
            company: {
                name: "Smith & Associates Law Firm",
                type: "Law Firm",
                registrationNumber: "LF-2023-001234",
                address: "123 Legal Street, Downtown, NY 10001",
                phone: "(*************",
                email: "<EMAIL>",
                website: "www.smithlaw.com",
                foundedYear: "2015",
                employeeCount: "25-50"
            },
            license: {
                businessLicense: "BL-NY-2023-5678",
                issueDate: "January 15, 2023",
                expiryDate: "January 15, 2024",
                issuingAuthority: "New York State Department of State",
                status: "Active",
                specializations: ["Corporate Law", "Intellectual Property", "Contract Law"]
            },
            employees: [
                {
                    name: "John Smith",
                    position: "Senior Partner",
                    department: "Corporate Law",
                    joinDate: "2015-03-01",
                    email: "<EMAIL>"
                },
                {
                    name: "Sarah Johnson",
                    position: "Associate Attorney",
                    department: "Intellectual Property",
                    joinDate: "2020-06-15",
                    email: "<EMAIL>"
                },
                {
                    name: "Michael Brown",
                    position: "Legal Assistant",
                    department: "General Practice",
                    joinDate: "2021-09-01",
                    email: "<EMAIL>"
                }
            ],
            jobs: [
                {
                    title: "Legal Research Assistant",
                    category: "Legal Research Assistance",
                    status: "Active",
                    applicants: 12,
                    postedDate: "2024-01-05",
                    paymentCode: "ETB 200",
                    paymentType: "Hourly"
                },
                {
                    title: "Document Preparation Specialist",
                    category: "Preparing Legal Filings & Case Documents",
                    status: "Active",
                    applicants: 8,
                    postedDate: "2024-01-03",
                    paymentCode: "ETB 180",
                    paymentType: "Hourly"
                },
                {
                    title: "Corporate Legal Advisor",
                    category: "Corporate Legal Advisory Support",
                    status: "Closed",
                    applicants: 25,
                    postedDate: "2023-12-20",
                    paymentCode: "ETB 300",
                    paymentType: "Monthly"
                }
            ]
        };

        function goBack() {
            window.history.back();
        }

        function showModal(title, content) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal-overlay').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('modal-overlay').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showCompanyInfo() {
            const company = employerData.company;
            const content = `
                <div class="info-section">
                    <div class="info-item">
                        <strong>Company Name:</strong>
                        <span>${company.name}</span>
                    </div>
                    <div class="info-item">
                        <strong>Type:</strong>
                        <span>${company.type}</span>
                    </div>
                    <div class="info-item">
                        <strong>Registration Number:</strong>
                        <span>${company.registrationNumber}</span>
                    </div>
                    <div class="info-item">
                        <strong>Address:</strong>
                        <span>${company.address}</span>
                    </div>
                    <div class="info-item">
                        <strong>Phone:</strong>
                        <span>${company.phone}</span>
                    </div>
                    <div class="info-item">
                        <strong>Email:</strong>
                        <span>${company.email}</span>
                    </div>
                    <div class="info-item">
                        <strong>Website:</strong>
                        <span>${company.website}</span>
                    </div>
                    <div class="info-item">
                        <strong>Founded:</strong>
                        <span>${company.foundedYear}</span>
                    </div>
                    <div class="info-item">
                        <strong>Employee Count:</strong>
                        <span>${company.employeeCount}</span>
                    </div>
                </div>
            `;
            showModal('Company Information', content);
        }

        function showLicenseInfo() {
            const license = employerData.license;
            const content = `
                <div class="info-section">
                    <div class="info-item">
                        <strong>Business License:</strong>
                        <span>${license.businessLicense}</span>
                    </div>
                    <div class="info-item">
                        <strong>Issue Date:</strong>
                        <span>${license.issueDate}</span>
                    </div>
                    <div class="info-item">
                        <strong>Expiry Date:</strong>
                        <span>${license.expiryDate}</span>
                    </div>
                    <div class="info-item">
                        <strong>Issuing Authority:</strong>
                        <span>${license.issuingAuthority}</span>
                    </div>
                    <div class="info-item">
                        <strong>Status:</strong>
                        <span class="status-active">${license.status}</span>
                    </div>
                    <div class="info-item">
                        <strong>Specializations:</strong>
                        <div class="specializations">
                            ${license.specializations.map(spec => `<span class="spec-tag">${spec}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `;
            showModal('License Information', content);
        }

        function showEmployeeInfo() {
            const employees = employerData.employees;
            const content = `
                <div class="info-section">
                    <div class="employee-count">
                        <strong>Total Employees: ${employees.length}</strong>
                    </div>
                    ${employees.map(employee => `
                        <div class="employee-card">
                            <div class="employee-header">
                                <h4>${employee.name}</h4>
                                <span class="employee-position">${employee.position}</span>
                            </div>
                            <div class="employee-details">
                                <div class="info-item">
                                    <strong>Department:</strong>
                                    <span>${employee.department}</span>
                                </div>
                                <div class="info-item">
                                    <strong>Join Date:</strong>
                                    <span>${new Date(employee.joinDate).toLocaleDateString()}</span>
                                </div>
                                <div class="info-item">
                                    <strong>Email:</strong>
                                    <span>${employee.email}</span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            showModal('Employee Information', content);
        }

        function showJobInfo() {
            const jobs = employerData.jobs;
            const content = `
                <div class="info-section">
                    <div class="job-stats">
                        <div class="stat-item">
                            <strong>Total Jobs:</strong> ${jobs.length}
                        </div>
                        <div class="stat-item">
                            <strong>Active Jobs:</strong> ${jobs.filter(job => job.status === 'Active').length}
                        </div>
                    </div>
                    ${jobs.map(job => `
                        <div class="job-card">
                            <div class="job-header">
                                <h4>${job.title}</h4>
                                <span class="job-status ${job.status.toLowerCase()}">${job.status}</span>
                            </div>
                            <div class="job-details">
                                <div class="info-item">
                                    <strong>Category:</strong>
                                    <span>${job.category}</span>
                                </div>
                                <div class="info-item">
                                    <strong>Posted Date:</strong>
                                    <span>${new Date(job.postedDate).toLocaleDateString()}</span>
                                </div>
                                <div class="info-item">
                                    <strong>Applicants:</strong>
                                    <span>${job.applicants}</span>
                                </div>
                                <div class="info-item">
                                    <strong>Payment:</strong>
                                    <span>${job.paymentCode} - ${job.paymentType}</span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            showModal('Job Postings', content);
        }

        function showSettings() {
            const content = `
                <div class="info-section">
                    <div class="settings-group">
                        <h4>Account Settings</h4>
                        <div class="setting-item">
                            <span>Change Password</span>
                            <button class="setting-btn">Update</button>
                        </div>
                        <div class="setting-item">
                            <span>Email Notifications</span>
                            <button class="setting-btn">Configure</button>
                        </div>
                    </div>
                    <div class="settings-group">
                        <h4>Privacy Settings</h4>
                        <div class="setting-item">
                            <span>Profile Visibility</span>
                            <button class="setting-btn">Manage</button>
                        </div>
                        <div class="setting-item">
                            <span>Data Export</span>
                            <button class="setting-btn">Download</button>
                        </div>
                    </div>
                    <div class="settings-group">
                        <h4>Support</h4>
                        <div class="setting-item">
                            <span>Help Center</span>
                            <button class="setting-btn">Visit</button>
                        </div>
                        <div class="setting-item">
                            <span>Contact Support</span>
                            <button class="setting-btn">Contact</button>
                        </div>
                    </div>
                </div>
            `;
            showModal('Settings', content);
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
