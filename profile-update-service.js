// Profile Update Service with <PERSON><PERSON>r Handling
import { 
    updateEmail, 
    updatePassword, 
    reauthenticateWithCredential, 
    EmailAuthProvider,
    updateProfile 
} from 'firebase/auth';
import { doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from './firebase-config.js';

class ProfileUpdateService {
    
    async updateUserEmail(newEmail, currentPassword) {
        const user = auth.currentUser;
        
        if (!user) {
            return { success: false, error: 'Please sign in to update your email', requiresAuth: true };
        }
        
        try {
            // Re-authenticate user
            const credential = EmailAuthProvider.credential(user.email, currentPassword);
            await reauthenticateWithCredential(user, credential);
            
            // Update email in Firebase Auth
            await updateEmail(user, newEmail);
            
            // Update email in Firestore
            await updateDoc(doc(db, 'users', user.uid), {
                email: newEmail,
                updatedAt: serverTimestamp()
            });
            
            return { success: true, message: 'Em<PERSON> updated successfully' };
            
        } catch (error) {
            return this.handleAuthError(error);
        }
    }
    
    async updateUserPassword(currentPassword, newPassword) {
        const user = auth.currentUser;
        
        if (!user) {
            return { success: false, error: 'Please sign in to update your password', requiresAuth: true };
        }
        
        // Validate new password
        const validation = this.validatePassword(newPassword);
        if (!validation.valid) {
            return { success: false, error: validation.error };
        }
        
        try {
            // Re-authenticate user
            const credential = EmailAuthProvider.credential(user.email, currentPassword);
            await reauthenticateWithCredential(user, credential);
            
            // Update password
            await updatePassword(user, newPassword);
            
            // Update timestamp in Firestore
            await updateDoc(doc(db, 'users', user.uid), {
                passwordUpdatedAt: serverTimestamp(),
                updatedAt: serverTimestamp()
            });
            
            return { success: true, message: 'Password updated successfully' };
            
        } catch (error) {
            return this.handleAuthError(error);
        }
    }
    
    async updateUserProfile(profileData) {
        const user = auth.currentUser;
        
        if (!user) {
            return { success: false, error: 'Please sign in to update your profile', requiresAuth: true };
        }
        
        try {
            // Update display name in Firebase Auth if provided
            if (profileData.name) {
                await updateProfile(user, {
                    displayName: profileData.name
                });
            }
            
            // Update profile data in Firestore
            await updateDoc(doc(db, 'users', user.uid), {
                ...profileData,
                updatedAt: serverTimestamp()
            });
            
            return { success: true, message: 'Profile updated successfully' };
            
        } catch (error) {
            return { 
                success: false, 
                error: 'Failed to update profile. Please try again.' 
            };
        }
    }
    
    validatePassword(password) {
        if (!password || password.length < 6) {
            return { valid: false, error: 'Password must be at least 6 characters long' };
        }
        
        if (!/(?=.*[a-z])/.test(password)) {
            return { valid: false, error: 'Password must contain at least one lowercase letter' };
        }
        
        if (!/(?=.*[A-Z])/.test(password)) {
            return { valid: false, error: 'Password must contain at least one uppercase letter' };
        }
        
        if (!/(?=.*\d)/.test(password)) {
            return { valid: false, error: 'Password must contain at least one number' };
        }
        
        return { valid: true };
    }
    
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    handleAuthError(error) {
        console.error('Auth error:', error);
        
        switch (error.code) {
            case 'auth/email-already-in-use':
                return { success: false, error: 'This email is already registered with another account' };
            
            case 'auth/invalid-email':
                return { success: false, error: 'Please enter a valid email address' };
            
            case 'auth/requires-recent-login':
                return { 
                    success: false, 
                    error: 'For security reasons, please sign in again to make this change',
                    requiresReauth: true 
                };
            
            case 'auth/wrong-password':
                return { success: false, error: 'Current password is incorrect' };
            
            case 'auth/weak-password':
                return { success: false, error: 'Password is too weak. Please choose a stronger password' };
            
            case 'auth/network-request-failed':
                return { success: false, error: 'Network error. Please check your connection and try again' };
            
            case 'auth/too-many-requests':
                return { success: false, error: 'Too many failed attempts. Please try again later' };
            
            default:
                return { 
                    success: false, 
                    error: 'An unexpected error occurred. Please try again.' 
                };
        }
    }
    
    // Helper method to check if user needs to re-authenticate
    async checkRecentAuth() {
        const user = auth.currentUser;
        if (!user) return false;
        
        // Check if last sign-in was recent (within 5 minutes)
        const lastSignIn = new Date(user.metadata.lastSignInTime);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        
        return lastSignIn > fiveMinutesAgo;
    }
}

export default new ProfileUpdateService();
