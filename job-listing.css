:root {
    --color-primary: #d17e7e;
    --color-primary-dark: #c06c6c;
    --color-secondary: #5a3e5d;
    --color-secondary-light: #8c6c8e;
    --color-secondary-dark: #4a3249;
    --color-bg-light: #f8f0eb;
    --color-bg-light-alt: #f5e6e0;
    --color-white: #ffffff;
    --color-text: #333333;
    --color-text-light: #666666;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: var(--color-bg-light);
    min-height: 100vh;
    color: var(--color-text);
}

.container {
    max-width: 600px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--color-white);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    width: 50px;
    height: 50px;
    background: var(--color-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: var(--color-secondary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.welcome-text {
    color: var(--color-white);
    font-size: 14px;
    font-weight: 500;
}

.year-badge {
    background: var(--color-white);
    color: var(--color-secondary);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.main-content {
    padding: 30px 20px 100px;
    flex: 1;
}

.title {
    font-size: 28px;
    font-weight: 700;
    color: var(--color-secondary);
    text-align: center;
    margin-bottom: 30px;
}

.search-section {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
}

.search-container {
    flex: 1;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 14px 50px 14px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    background: var(--color-white);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: var(--color-bg-light);
}

.search-icon {
    font-size: 18px;
}

.filter-btn {
    background: var(--color-secondary);
    color: var(--color-white);
    border: none;
    border-radius: 12px;
    padding: 14px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: var(--color-secondary-light);
}

.filter-icon {
    font-size: 18px;
}

.jobs-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.job-card {
    background: var(--color-white);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-align: center;
}

.job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-color: var(--color-primary);
}

.job-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    background: var(--color-bg-light);
    border-radius: 12px;
}

.job-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 12px;
    line-height: 1.3;
}

.job-rating {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-bottom: 8px;
}

.star {
    font-size: 16px;
    color: #ddd;
    cursor: pointer;
    transition: color 0.3s ease;
}

.star.filled {
    color: #ffd700;
}

.job-details {
    font-size: 12px;
    color: var(--color-text-light);
    margin-top: 8px;
}

.no-jobs-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--color-text-light);
}

.no-jobs-message p {
    margin-bottom: 8px;
    font-size: 16px;
}

.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 600px;
    background: var(--color-white);
    border-top: 1px solid #e1e5e9;
    display: flex;
    padding: 12px 0 8px;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    padding: 8px;
    transition: all 0.3s ease;
}

.nav-item.active {
    color: var(--color-primary);
}

.nav-item:hover {
    color: var(--color-primary);
}

.nav-icon {
    font-size: 20px;
}

.nav-label {
    font-size: 12px;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
        box-shadow: none;
    }
    
    .main-content {
        padding: 20px 15px 100px;
    }
    
    .jobs-grid {
        gap: 16px;
    }
    
    .job-card {
        padding: 16px;
    }
    
    .job-icon {
        width: 50px;
        height: 50px;
        font-size: 28px;
    }
    
    .job-title {
        font-size: 14px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 800px;
    }
    
    .main-content {
        padding: 40px 40px 120px;
    }
    
    .jobs-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
    }
}