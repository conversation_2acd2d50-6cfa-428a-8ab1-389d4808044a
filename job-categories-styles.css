/* Custom color variables */
:root {
  --color-primary: #d17e7e;
  --color-primary-dark: #c06c6c;
  --color-secondary: #5a3e5d;
  --color-secondary-light: #8c6c8e;
  --color-secondary-dark: #4a3249;
  --color-bg-light: #f8f0eb;
  --color-bg-light-alt: #f5e6e0;
  --color-white: #ffffff;
  --color-text: #333333;
  --color-text-light: #666666;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--color-bg-light);
  min-height: 100vh;
  display: flex;
  justify-content: center;
  padding: 20px;
}

.container {
  width: 100%;
  max-width: 500px;
  background: var(--color-white);
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--color-white);
}

.logo {
  width: 40px;
  height: 40px;
  background: var(--color-white);
  color: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-white);
  letter-spacing: -0.5px;
}

.spacer {
  width: 40px;
}



.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.job-categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.job-category-card {
  background: var(--color-white);
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.job-category-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(209, 126, 126, 0.2);
}

.job-category-card:active {
  transform: translateY(-2px);
}

.card-icon {
  margin-bottom: 15px;
  color: var(--color-secondary);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.overlay-icon {
  position: absolute;
  top: 12px;
  right: -8px;
  opacity: 0.6;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 12px;
  line-height: 1.3;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-rating {
  display: flex;
  justify-content: center;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #e5e7eb;
  font-size: 16px;
  transition: color 0.3s ease;
}

.star.filled {
  color: #fbbf24;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 600px;
  background: var(--color-white);
  border-top: 1px solid #e1e5e9;
  display: flex;
  padding: 12px 0 8px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 8px;
  transition: all 0.3s ease;
  color: var(--color-text-light);
}

.nav-item.active {
  color: var(--color-primary);
}

.nav-item:hover {
  color: var(--color-primary);
}

.nav-icon {
  font-size: 20px;
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }

  .content {
    padding: 15px;
  }

  .job-categories-grid {
    gap: 15px;
  }

  .job-category-card {
    padding: 15px;
  }

  .card-title {
    font-size: 13px;
    min-height: 36px;
  }

  .card-icon svg {
    width: 40px;
    height: 40px;
  }
}

/* Animation for page load */
.container {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover animation */
.job-category-card {
  position: relative;
}

.job-category-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 10px;
}

.job-category-card:hover::before {
  opacity: 0.05;
}

.job-category-card > * {
  position: relative;
  z-index: 1;
}

/* Focus states for accessibility */
.job-category-card:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
