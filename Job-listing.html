<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Listing - Legal Career Platform</title>
    <link rel="stylesheet" href="job-listing.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">S</div>
            <div class="user-info" id="userInfo">
                <span class="welcome-text">Welcome, <span id="userName">Student</span></span>
                <span class="year-badge" id="yearBadge">Year 2</span>
            </div>
        </div>
        
        <div class="main-content">
            <h1 class="title">Job Listing</h1>
            
            <div class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search jobs..." id="searchInput">
                    <button class="search-btn" id="searchBtn">
                        <span class="search-icon">🔍</span>
                    </button>
                </div>
                <button class="filter-btn" id="filterBtn">
                    <span class="filter-icon">☰</span>
                </button>
            </div>
            
            <div class="jobs-grid" id="jobsGrid">
                <!-- Jobs will be dynamically loaded here -->
            </div>
            
            <div class="no-jobs-message" id="noJobsMessage" style="display: none;">
                <p>No jobs available for your current year of study.</p>
                <p>Check back later for new opportunities!</p>
            </div>
        </div>
        
        <div class="bottom-nav">
            <div class="nav-item active">
                <span class="nav-icon">🏠</span>
                <span class="nav-label">Home</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">🔍</span>
                <span class="nav-label">Search</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">💬</span>
                <span class="nav-label">Messages</span>
            </div>
            <div class="nav-item">
                <span class="nav-icon">👤</span>
                <span class="nav-label">Profile</span>
            </div>
        </div>
    </div>

    <script src="job-listing.js"></script>
</body>
</html>